import { useState, useCallback } from 'react';
import type {KLineDataPoint, KLineConfig, LoadingState } from '@/types';
import { stockApi } from '@/services/stockApi';

export const useKline = () => {
    const [klineData, setKlineData] = useState<KLineDataPoint[]>([]);
    const [loadingState, setLoadingState] = useState<LoadingState>({
        isLoading: false,
        error: undefined,
    });

    const fetchKLineData = useCallback(async (symbol: string, config: KLineConfig) => {
        setLoadingState({ isLoading: true, error: undefined });

        try {
            const data = await stockApi.getKLineData(symbol, config);
            console.log(data);
            setKlineData(data.data?.data || []);
            setLoadingState({ isLoading: false });
            return data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '获取K线数据失败';
            setLoadingState({ isLoading: false, error: errorMessage });
            throw error;
        }
    }, []);


    return {
        klineData,
        loadingState,
        fetchKLineData,
    };
};
