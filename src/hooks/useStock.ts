import { useState, useCallback } from 'react';
import type { StockBaseInfo, LoadingState } from '@/types';
import { stockApi } from '@/services/stockApi';

export const useStock = () => {
    const [stockInfo, setStockInfo] = useState<StockBaseInfo | null>(null);
    const [loadingState, setLoadingState] = useState<LoadingState>({
        isLoading: false,
        error: undefined,
    });

    const fetchStockInfo = useCallback(async (symbol: string) => {
        setLoadingState({ isLoading: true, error: undefined });

        try {
            const response = await stockApi.getStockInfo(symbol);
            setStockInfo(response.data);
            setLoadingState({ isLoading: false });
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '获取股票信息失败';
            setLoadingState({ isLoading: false, error: errorMessage });
            throw error;
        }
    }, []);

    return {
        stockInfo,
        loadingState,
        fetchStockInfo,
    };
};
