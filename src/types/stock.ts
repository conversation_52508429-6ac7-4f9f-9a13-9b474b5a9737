// 股票基本信息 - 根据API响应结构定义
export interface StockBaseInfo {
    code: string;            // 股票代码
    name: string;            // 股票名称
    pe: number;              // 市盈率
    pb: number;              // 市净率
    industry: string;        // 行业
    mc: string;              // 市值
    cmc: string;             // 流通市值
    block_id: string;        // 板块ID
    roe: number;             // 净资产收益率
    npr: number;             // 净利润率
    np: number;              // 净利润
    gpr: number;             // 毛利率
}

// 基本信息API响应
export interface StockBaseInfoResponse {
    success: boolean;
    message: string;
    data: StockBaseInfo;
    code: number;
}

// K线数据点 - 根据API响应结构定义
export interface KLineDataPoint {
    name: string;            // 股票名称
    code: string;            // 股票代码
    date: string;            // 日期
    open: number;            // 开盘价
    close: number;           // 收盘价
    high: number;            // 最高价
    low: number;             // 最低价
    volume: number;          // 成交量
    amount: number;          // 成交额
    amplitude: number;       // 振幅
    change_percent: number;  // 涨跌幅
    change_amount: number;   // 涨跌额
    turnover: number;        // 换手率
}

// K线数据响应
export interface KLineDataResponse {
    success: boolean;
    message: string;
    data: {
        stock_code: string;      // 股票代码
        stock_name: string;      // 股票名称
        period: number;          // 周期
        data: KLineDataPoint[];  // K线数据数组
    };
    code: number;
    stock_code: string;      // 股票代码
    stock_name: string;      // 股票名称
    period: number;          // 周期
}

export interface KLineConfig {
    period?: number;         // 周期，默认为日线        // 数据条数限制
}
