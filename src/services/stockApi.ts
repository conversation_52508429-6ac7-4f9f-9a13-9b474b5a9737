import api from './api';
import type { StockBaseInfoResponse, KLineDataResponse, KLineConfig } from '@/types';

// 股票API服务类
export class StockApiService {
    /**
     * 获取股票基本信息
     * @param symbol 股票代码
     */
    static async getStockInfo(symbol: string): Promise<StockBaseInfoResponse> {
        try {
            const response = await api.get<StockBaseInfoResponse>(`/api/stock/base-info/${symbol}`);
            return response.data;
        } catch (error) {
            throw new Error(`获取股票 ${symbol} 信息失败: ${error}`);
        }
    }

    /**
     * 获取K线数据
     * @param symbol 股票代码
     * @param config K线配置
     */
    static async getKLineData(symbol: string, config: KLineConfig): Promise<KLineDataResponse> {
        try {
            const response = await api.get<KLineDataResponse>(`/api/stock/kline/${symbol}`, { params: config });
            console.log(response.data);
            return response.data;
        } catch (error) {
            throw new Error(`获取股票 ${symbol} K线数据失败: ${error}`);
        }
    }
}

// 导出便捷方法
export const stockApi = {
    getStockInfo: StockApiService.getStockInfo.bind(StockApiService),
    getKLineData: StockApiService.getKLineData.bind(StockApiService),
};
