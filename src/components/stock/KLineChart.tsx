import React, { useEffect, useRef } from 'react';
import { init, dispose } from 'klinecharts';
import type { KLineDataPoint } from '@/types';

interface KLineChartProps {
  data: KLineDataPoint[];
  symbol: string;
}

export const KLineChart: React.FC<KLineChartProps> = ({ data, symbol }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);

  useEffect(() => {
    if (chartRef.current && !chartInstance.current) {
      // 初始化图表
      chartInstance.current = init(chartRef.current);
      
      // 配置图表样式
      chartInstance.current.setStyles({
        grid: {
          horizontal: {
            color: '#E5E7EB',
          },
          vertical: {
            color: '#E5E7EB',
          },
        },
        candle: {
          priceMark: {
            high: {
              color: '#10B981',
            },
            low: {
              color: '#EF4444',
            },
          },
          tooltip: {
            labels: ['时间', '开', '收', '高', '低', '成交量'],
          },
        },
        xAxis: {
          axisLine: {
            color: '#D1D5DB',
          },
          tickText: {
            color: '#6B7280',
          },
        },
        yAxis: {
          axisLine: {
            color: '#D1D5DB',
          },
          tickText: {
            color: '#6B7280',
          },
        },
      });

      // 创建主图
      chartInstance.current.createIndicator('MA', true, { id: 'candle_pane' });
      
      // 创建成交量副图
      chartInstance.current.createIndicator('VOL', false, { height: 80 });

      chartInstance.current.createIndicator('MACD', false, { height: 80 });
    }

    return () => {
      if (chartInstance.current) {
        dispose(chartRef.current!);
        chartInstance.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (chartInstance.current && data.length > 0) {
      // 转换数据格式
      const klineData = data.map(item => ({
        timestamp: new Date(item.date).getTime(),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
      }));
      chartInstance.current.setPeriod({ span: 1, type: 'day' })
      // 添加数据到图表
      chartInstance.current.applyNewData(klineData);
    }
  }, [data]);

  return (
    <div className="w-full">
      <div className="mb-4">
        <h2 className="text-xl font-bold text-gray-900">{symbol} K线图</h2>
        <p className="text-sm text-gray-600">实时股价走势图表</p>
      </div>
      <div 
        ref={chartRef} 
        className="w-full h-96 border border-gray-200 rounded-lg bg-white"
      />
    </div>
  );
};
