import React from 'react';
import { TrendingUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { StockBaseInfo } from '@/types';

interface StockCardProps {
  stock: StockBaseInfo;
  onClick?: () => void;
}

export const StockCard: React.FC<StockCardProps> = ({ stock, onClick }) => {
  // 由于StockBaseInfo没有涨跌信息，默认显示为中性状态
  const isPositive = true; // 可以根据实际需求调整
  const bgColor = isPositive ? 'bg-green-50' : 'bg-red-50';

  const formatNumber = (num: number | null, decimals = 2) => {
    if (num === null || num === undefined) {
      return '暂无数据';
    }
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  };

  const formatMarketCap = (mc: string) => {
    return mc || '暂无数据';
  };

  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-lg ${bgColor} border-l-4 ${
        isPositive ? 'border-l-green-500' : 'border-l-red-500'
      }`}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div>
            <span className="text-lg font-bold">{stock.code}</span>
            <span className="text-sm text-gray-600 ml-2">{stock.name}</span>
          </div>
          <TrendingUp className="h-5 w-5 text-blue-600" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-lg font-semibold text-gray-800">{stock.industry}</div>
            <div className="text-sm text-gray-600 mt-1">
              板块ID: {stock.block_id}
            </div>
          </div>
          <div className="text-right text-sm text-gray-600">
            <div>市值: {formatMarketCap(stock.mc)}</div>
            <div>流通市值: {formatMarketCap(stock.cmc)}</div>
          </div>
        </div>
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
            <div>
              <div>市盈率: {formatNumber(stock.pe)}</div>
              <div>市净率: {formatNumber(stock.pb)}</div>
              <div>净资产收益率: {stock.roe !== null ? `${formatNumber(stock.roe)}%` : '暂无数据'}</div>
            </div>
            <div>
              <div>净利润率: {stock.npr !== null ? `${formatNumber(stock.npr)}%` : '暂无数据'}</div>
              <div>净利润: {formatNumber(stock.np)}</div>
              <div>毛利率: {stock.gpr !== null ? `${formatNumber(stock.gpr)}%` : '暂无数据'}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
