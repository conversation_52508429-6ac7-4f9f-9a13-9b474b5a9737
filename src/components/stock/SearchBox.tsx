import React, { useState } from 'react';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface SearchBoxProps {
  onSearch: (symbol: string) => void;
  isLoading?: boolean;
}

export const SearchBox: React.FC<SearchBoxProps> = ({ onSearch, isLoading = false }) => {
  const [symbol, setSymbol] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (symbol.trim()) {
      onSearch(symbol.trim().toUpperCase());
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex w-full max-w-md space-x-2">
      <Input
        type="text"
        placeholder="输入股票代码 (如: AAPL, TSLA)"
        value={symbol}
        onChange={(e) => setSymbol(e.target.value)}
        className="flex-1"
        disabled={isLoading}
      />
      <Button type="submit" disabled={isLoading || !symbol.trim()}>
        <Search className="h-4 w-4 mr-2" />
        搜索
      </Button>
    </form>
  );
};
