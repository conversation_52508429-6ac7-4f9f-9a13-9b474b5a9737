import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { KLineChart } from '@/components/stock/KLineChart';
import { useKline } from '@/hooks/useKline';
import type { KLineConfig } from '@/types';

export const StockDetailPage: React.FC = () => {
    const { symbol } = useParams<{ symbol: string }>();
    const navigate = useNavigate();
    const { klineData, loadingState, fetchKLineData } = useKline();
    const [refreshing, setRefreshing] = useState(false);

    // 获取K线数据
    const loadKLineData = useCallback(async () => {
        if (!symbol) return;
        
        setRefreshing(true);
        try {
            const config: KLineConfig = {
                period: 101 // 日线
            };
            await fetchKLineData(symbol, config);
        } catch (error) {
            console.error('获取K线数据失败:', error);
        } finally {
            setRefreshing(false);
        }
    }, [symbol, fetchKLineData]);

    useEffect(() => {
        if (!symbol) return;
        
        const loadData = async () => {
            try {
                const config: KLineConfig = {
                    period: 101 // 日线
                };
                await fetchKLineData(symbol, config);
            } catch (error) {
                console.error('获取K线数据失败:', error);
            }
        };
        
        loadData();
    }, [symbol]);

    // 如果没有股票代码，显示错误页面
    if (!symbol) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">股票代码无效</h1>
                    <Button onClick={() => navigate('/')}>返回首页</Button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="container mx-auto px-4 py-6">
                {/* 头部导航 */}
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate('/')}
                            className="flex items-center space-x-2"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            <span>返回</span>
                        </Button>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {symbol} 股票详情
                        </h1>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={loadKLineData}
                        disabled={loadingState.isLoading || refreshing}
                        className="flex items-center space-x-2"
                    >
                        <RefreshCw className={`h-4 w-4 ${(loadingState.isLoading || refreshing) ? 'animate-spin' : ''}`} />
                        <span>刷新</span>
                    </Button>
                </div>

                {/* 错误提示 */}
                {loadingState.error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <p className="text-red-800">{loadingState.error}</p>
                    </div>
                )}

                {/* K线图表 */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                    {loadingState.isLoading ? (
                        <div className="flex items-center justify-center h-96">
                            <div className="text-center">
                                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
                                <p className="text-gray-600">正在加载K线数据...</p>
                            </div>
                        </div>
                    ) : klineData.length > 0 ? (
                        <KLineChart data={klineData} symbol={symbol} />
                    ) : (
                        <div className="flex items-center justify-center h-96">
                            <div className="text-center">
                                <p className="text-gray-600 mb-4">暂无K线数据</p>
                                <Button onClick={loadKLineData} variant="outline">
                                    重新加载
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
