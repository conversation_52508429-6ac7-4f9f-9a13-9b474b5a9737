import React from "react";
import { useNavigate } from "react-router-dom";
import { SearchBox } from "@/components/stock/SearchBox";
import { StockCard } from "@/components/stock/StockCard";
import { type StockBaseInfo } from "@/types";
import { useStock } from "@/hooks/useStock";

export const HomePage: React.FC = () => {
    const { stockInfo, loadingState, fetchStockInfo } = useStock();
    const navigate = useNavigate();

    const handleSearch = async (symbol: string) => {
        await fetchStockInfo(symbol);
    };

    const handleStockCardClick = (stock: StockBaseInfo) => {
        if (stock) {
            navigate(`/stock/${stock.code}`);
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="container mx-auto px-4">
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-bold text-gray-900 mb-2">股票查询系统</h1>
                    <p className="text-gray-600">输入股票代码查看实时股价信息和K线图</p>
                </div>

                <div className="flex justify-center mb-8">
                    <SearchBox onSearch={handleSearch} isLoading={loadingState.isLoading} />
                </div>

                {loadingState.isLoading && (
                    <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                )}

                {loadingState.error && (
                    <div className="max-w-md mx-auto">
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                            {loadingState.error}
                        </div>
                    </div>
                )}

                {stockInfo && !loadingState.isLoading && (
                    <div className="max-w-md mx-auto">
                        <StockCard stock={stockInfo} onClick={() => handleStockCardClick(stockInfo)} />
                        <p className="text-center text-sm text-gray-500 mt-2">点击卡片查看K线图</p>
                    </div>
                )}
            </div>
        </div>
    );
};
