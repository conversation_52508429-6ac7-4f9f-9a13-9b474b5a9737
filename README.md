# 股票查询系统 (Quantify Web)

一个基于 React + TypeScript 的股票查询和K线图展示应用。

## 功能特性

- 🔍 股票代码搜索
- 📊 实时股票信息展示
- 📈 K线图表展示
- 📱 响应式设计
- 🎨 现代化UI界面

## 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件**: Radix UI
- **样式**: Tailwind CSS
- **路由**: React Router V6
- **HTTP客户端**: Axios
- **图标**: Lucide React
- **图表库**: KLineCharts
- **动画**: Tailwind CSS Animate

## 项目结构

```
src/
├── components/          # 组件目录
│   ├── ui/             # 基础UI组件
│   └── stock/          # 股票相关组件
├── pages/              # 页面组件
├── hooks/              # 自定义Hooks
├── services/           # API服务层
├── types/              # TypeScript类型定义
├── lib/                # 工具库
└── utils/              # 工具函数
```

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

项目将在 `http://localhost:5173` 启动。

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```

## API配置

项目使用环境变量配置API地址：

```bash
# .env
VITE_API_BASE_URL=http://localhost:8000
```

### API接口

#### 1. 获取股票基本信息
```
GET /api/stock/base-info/:stock_code
```

#### 2. 获取K线数据
```
GET /api/stock/kline/:stock_code
```

## 使用说明

1. **主页面**: 在搜索框中输入股票代码（如 AAPL, TSLA）
2. **搜索**: 点击搜索按钮获取股票信息
3. **查看详情**: 点击股票信息卡片跳转到K线图页面
4. **K线图**: 查看股票的历史价格走势

## 主要组件

### SearchBox
股票搜索组件，支持输入股票代码进行搜索。

### StockCard
股票信息卡片，展示股票的基本信息包括价格、涨跌幅、成交量等。

### KLineChart
K线图组件，使用 KLineCharts 库展示股票的价格走势。

## API服务

项目包含完整的API服务层设计：

- `stockApi.ts`: 股票数据API服务
- `api.ts`: 基础HTTP客户端配置
- `useStock.ts`: 股票数据管理Hook

目前使用模拟数据，可以轻松替换为真实的股票API。

## 开发说明

- 项目使用 TypeScript 进行类型安全开发
- 组件采用函数式组件 + Hooks 模式
- 样式使用 Tailwind CSS 实用类
- 路径别名配置：`@/` 指向 `src/` 目录

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 许可证

MIT License